// 演示数据 - 用于测试临时变量智能提示修复

// 测试场景1: 简单对象属性访问
const testData1 = [
  {
    id: "1",
    key: "user",
    type: "object",
    description: "用户信息",
    children: [
      { id: "1-1", key: "name", type: "string", description: "用户名" },
      { id: "1-2", key: "age", type: "number", description: "年龄" },
      { id: "1-3", key: "email", type: "string", description: "邮箱" }
    ]
  }
];

// 测试场景2: 嵌套对象属性访问
const testData2 = [
  {
    id: "2",
    key: "order",
    type: "object",
    description: "订单信息",
    children: [
      {
        id: "2-1",
        key: "customer",
        type: "object",
        description: "客户信息",
        children: [
          { id: "2-1-1", key: "name", type: "string", description: "客户姓名" },
          { id: "2-1-2", key: "phone", type: "string", description: "电话号码" }
        ]
      },
      { id: "2-2", key: "total", type: "number", description: "总金额" }
    ]
  }
];

// 测试场景3: 原生方法支持
const testData3 = [
  { id: "3-1", key: "userName", type: "string", description: "用户名" },
  { id: "3-2", key: "userList", type: "array", description: "用户列表" },
  { id: "3-3", key: "count", type: "number", description: "计数器" },
  { id: "3-4", key: "isActive", type: "boolean", description: "是否激活" }
];

// 测试场景4: ROOT节点展开
const testData4 = [
  {
    id: "root",
    key: "ROOT",
    type: "object",
    description: "根节点",
    children: [
      { id: "4-1", key: "headers", type: "object", description: "请求头" },
      { id: "4-2", key: "body", type: "string", description: "请求体" },
      { id: "4-3", key: "status", type: "number", description: "状态码" }
    ]
  }
];

// 测试场景5: 复合场景测试
const testData5 = [
  {
    id: "root",
    key: "ROOT",
    type: "object",
    children: [
      { id: "5-1", key: "title", type: "string", description: "标题" },
      {
        id: "5-2",
        key: "data",
        type: "object",
        description: "数据对象",
        children: [
          { id: "5-2-1", key: "items", type: "array", description: "项目列表" },
          { id: "5-2-2", key: "total", type: "number", description: "总数" }
        ]
      }
    ]
  }
];

// 导出测试数据
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testData1,
    testData2,
    testData3,
    testData4,
    testData5
  };
}

// 浏览器环境下的全局变量
if (typeof window !== 'undefined') {
  window.testData = {
    testData1,
    testData2,
    testData3,
    testData4,
    testData5
  };
}

// 使用示例:
// 在Vue组件中使用这些测试数据来验证智能提示功能
/*
import { updateIntelliSenseData } from './scriptCompletion';

// 测试原生方法支持
updateIntelliSenseData({
  currentVariables: testData3
});

// 测试ROOT节点展开
updateIntelliSenseData({
  currentVariables: testData4
});

// 在Monaco Editor中输入以下内容来测试:
// userName.  (应显示字符串方法)
// userList.  (应显示数组方法)
// headers.   (ROOT节点已展开，直接显示headers)
*/
