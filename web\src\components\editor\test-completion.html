<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时变量智能提示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        .test-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin-bottom: 15px;
        }
        .test-steps {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
        }
        .step {
            margin-bottom: 8px;
        }
        .expected {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #4caf50;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>临时变量智能提示测试指南</h1>
        <p>此页面提供了测试临时变量智能提示功能的详细指南。请在实际的Monaco Editor中进行测试。</p>

        <div class="test-section">
            <div class="test-title">测试场景 1: 简单对象属性访问</div>
            <div class="test-description">测试临时变量的直接子属性智能提示</div>
            <div class="test-data">
                测试数据结构:
                <pre>
currentVariables = [
  {
    id: "1",
    key: "user",
    type: "object",
    description: "用户信息",
    children: [
      { id: "1-1", key: "name", type: "string", description: "用户名" },
      { id: "1-2", key: "age", type: "number", description: "年龄" },
      { id: "1-3", key: "email", type: "string", description: "邮箱" }
    ]
  }
]
                </pre>
            </div>
            <div class="test-steps">
                <div class="step">1. 在编辑器中输入: <code>user.</code></div>
                <div class="step">2. 观察是否出现智能提示</div>
                <div class="step">3. 检查提示列表是否包含: name, age, email</div>
            </div>
            <div class="expected">
                <strong>预期结果:</strong> 应该显示 name、age、email 三个属性的智能提示
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试场景 2: 嵌套对象属性访问</div>
            <div class="test-description">测试多层嵌套对象的智能提示</div>
            <div class="test-data">
                测试数据结构:
                <pre>
currentVariables = [
  {
    id: "2",
    key: "order",
    type: "object",
    description: "订单信息",
    children: [
      {
        id: "2-1",
        key: "customer",
        type: "object",
        description: "客户信息",
        children: [
          { id: "2-1-1", key: "name", type: "string", description: "客户姓名" },
          { id: "2-1-2", key: "phone", type: "string", description: "电话号码" }
        ]
      },
      { id: "2-2", key: "total", type: "number", description: "总金额" }
    ]
  }
]
                </pre>
            </div>
            <div class="test-steps">
                <div class="step">1. 在编辑器中输入: <code>order.</code></div>
                <div class="step">2. 选择 customer 后输入: <code>order.customer.</code></div>
                <div class="step">3. 观察是否出现二级属性的智能提示</div>
            </div>
            <div class="expected">
                <strong>预期结果:</strong> 
                <br>- <code>order.</code> 应显示: customer, total
                <br>- <code>order.customer.</code> 应显示: name, phone
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试场景 3: 原生方法支持</div>
            <div class="test-description">测试临时变量的原生方法智能提示（如字符串的length、数组的push等）</div>
            <div class="test-data">
                测试数据结构:
                <pre>
currentVariables = [
  { id: "3-1", key: "userName", type: "string", description: "用户名" },
  { id: "3-2", key: "userList", type: "array", description: "用户列表" },
  { id: "3-3", key: "count", type: "number", description: "计数器" }
]
                </pre>
            </div>
            <div class="test-steps">
                <div class="step">1. 输入 <code>userName.</code> 应显示字符串原生方法（如 length, charAt, substring 等）</div>
                <div class="step">2. 输入 <code>userList.</code> 应显示数组原生方法（如 length, push, pop, forEach 等）</div>
                <div class="step">3. 输入 <code>count.</code> 应显示数字原生方法（如 toString, toFixed 等）</div>
            </div>
            <div class="expected">
                <strong>预期结果:</strong>
                <br>- userName 应有字符串方法: length, charAt, substring, indexOf 等
                <br>- userList 应有数组方法: length, push, pop, forEach, map 等
                <br>- count 应有数字方法: toString, toFixed, toPrecision 等
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试场景 4: ROOT节点展开</div>
            <div class="test-description">测试ROOT节点自动展开，不显示ROOT节点本身</div>
            <div class="test-data">
                测试数据结构:
                <pre>
currentVariables = [
  {
    id: "root",
    key: "ROOT",
    type: "object",
    description: "根节点",
    children: [
      { id: "4-1", key: "headers", type: "object", description: "请求头" },
      { id: "4-2", key: "body", type: "string", description: "请求体" },
      { id: "4-3", key: "status", type: "number", description: "状态码" }
    ]
  }
]
                </pre>
            </div>
            <div class="test-steps">
                <div class="step">1. 输入空白时按 Ctrl+Space，应直接显示 headers, body, status</div>
                <div class="step">2. 不应显示 ROOT 节点</div>
                <div class="step">3. 输入 <code>headers.</code> 应正常显示子属性</div>
            </div>
            <div class="expected">
                <strong>预期结果:</strong>
                <br>- 顶级提示应直接显示: headers, body, status
                <br>- 不应出现 ROOT 节点
                <br>- 各子节点功能正常
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试场景 5: 复合场景测试</div>
            <div class="test-description">测试ROOT节点展开 + 原生方法 + 嵌套对象的综合场景</div>
            <div class="test-data">
                测试数据结构:
                <pre>
currentVariables = [
  {
    id: "root",
    key: "ROOT",
    type: "object",
    children: [
      { id: "5-1", key: "title", type: "string", description: "标题" },
      {
        id: "5-2",
        key: "data",
        type: "object",
        description: "数据对象",
        children: [
          { id: "5-2-1", key: "items", type: "array", description: "项目列表" },
          { id: "5-2-2", key: "total", type: "number", description: "总数" }
        ]
      }
    ]
  }
]
                </pre>
            </div>
            <div class="test-steps">
                <div class="step">1. 顶级提示应显示: title, data（不显示ROOT）</div>
                <div class="step">2. 输入 <code>title.</code> 应显示字符串原生方法</div>
                <div class="step">3. 输入 <code>data.</code> 应显示: items, total</div>
                <div class="step">4. 输入 <code>data.items.</code> 应显示数组原生方法</div>
                <div class="step">5. 输入 <code>data.total.</code> 应显示数字原生方法</div>
            </div>
            <div class="expected">
                <strong>预期结果:</strong> 所有层级的智能提示都应正常工作，包括原生方法和自定义属性
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">调试信息</div>
            <div class="test-description">在浏览器控制台中查看调试信息</div>
            <div class="test-steps">
                <div class="step">1. 打开浏览器开发者工具 (F12)</div>
                <div class="step">2. 切换到 Console 标签页</div>
                <div class="step">3. 在编辑器中触发智能提示时观察日志输出</div>
                <div class="step">4. 查找以下关键日志:</div>
                <ul style="margin-left: 20px;">
                    <li><code>更新当前变量:</code> - 变量数据更新</li>
                    <li><code>智能提示触发:</code> - 提示触发信息</li>
                    <li><code>查找嵌套变量:</code> - 嵌套变量查找过程</li>
                    <li><code>找到目标变量:</code> - 找到的目标变量信息</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">常见问题排查</div>
            <div class="test-description">如果智能提示不工作，请检查以下项目</div>
            <div class="test-steps">
                <div class="step">1. 确认 enableIntellisense 属性为 true</div>
                <div class="step">2. 确认 currentVariables 数据格式正确</div>
                <div class="step">3. 确认变量数据包含 children 属性（对于对象类型）</div>
                <div class="step">4. 检查控制台是否有错误信息</div>
                <div class="step">5. 尝试手动触发智能提示 (Ctrl+Space)</div>
            </div>
        </div>
    </div>
</body>
</html>
